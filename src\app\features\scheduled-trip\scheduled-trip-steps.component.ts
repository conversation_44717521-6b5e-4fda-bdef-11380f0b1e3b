import { CommonModule } from '@angular/common';
import { Component, computed, OnD<PERSON>roy, OnInit, signal } from '@angular/core';
import { Router } from '@angular/router';
import { LatLng } from 'leaflet';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DialogModule } from 'primeng/dialog';
import { StepperModule } from 'primeng/stepper';
import { TagModule } from 'primeng/tag';
import { NominatimService } from '../../services/nominatim.service';
import { injectMany } from '../../shared/helpers/injectMany';
import { ReverseGeocodingResult } from '../../shared/types/nominatim.types';
import { NearPickupLocationStepComponent } from './steps/near-pickup-location-step.component';
import { OrderLocationStepComponent } from './steps/order-location-step.component';
import { TimeStepComponent } from './steps/time-step.component';

export enum ScheduledTripStep {
    ORDER_LOCATION = 0,
    TIME = 1,
    NEAR_PICKUP_LOCATION = 2,
    CONFIRM_SCHEDULED_TRIP = 3,
}

export interface ScheduledTripData {
    pickupLocation?: LatLng;
    dropoffLocation?: LatLng;
    pickupAddress?: string;
    dropoffAddress?: string;
    pickupAddressDetails?: ReverseGeocodingResult;
    dropoffAddressDetails?: ReverseGeocodingResult;
    scheduledDateTime?: Date;
    nearPickupLocation?: LatLng;
    nearPickupAddress?: string;
    nearPickupAddressDetails?: ReverseGeocodingResult;
}

@Component({
    selector: 'app-scheduled-trip-steps',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        CardModule,
        StepperModule,
        DialogModule,
        TagModule,
        OrderLocationStepComponent,
        TimeStepComponent,
        NearPickupLocationStepComponent,
    ],
    templateUrl: './scheduled-trip-steps.component.html',
    styles: `
        :host {
            display: block;
            flex-grow: 1;
            max-height: 97dvh;
        }
    `,
    providers: [MessageService],
})
export class ScheduledTripStepsComponent implements OnInit, OnDestroy {
    services = injectMany({
        MessageService,
        Router,
        NominatimService,
    });

    // Make enum available in template
    ScheduledTripStep = ScheduledTripStep;

    // State management
    currentStep = signal<ScheduledTripStep>(ScheduledTripStep.ORDER_LOCATION);
    isLoading = signal<boolean>(false);

    // Trip data
    tripData = signal<ScheduledTripData>({});

    // Computed properties
    canProceedToTime = computed(() => {
        const data = this.tripData();
        return (
            data.pickupLocation !== undefined &&
            data.dropoffLocation !== undefined
        );
    });

    canProceedToNearPickup = computed(() => {
        const data = this.tripData();
        return this.canProceedToTime() && data.scheduledDateTime !== undefined;
    });

    canConfirmTrip = computed(() => {
        const data = this.tripData();
        return (
            this.canProceedToNearPickup() &&
            data.nearPickupLocation !== undefined
        );
    });

    ngOnInit(): void {
        // Initialize component
    }

    ngOnDestroy(): void {
        // Cleanup if needed
    }

    /**
     * Handle order location step completion
     */
    onOrderLocationCompleted(data: {
        pickup: LatLng;
        dropoff: LatLng;
        pickupAddress?: string;
        dropoffAddress?: string;
    }): void {
        this.tripData.update((current) => ({
            ...current,
            pickupLocation: data.pickup,
            dropoffLocation: data.dropoff,
            pickupAddress: data.pickupAddress,
            dropoffAddress: data.dropoffAddress,
        }));
        this.proceedToNextStep();
    }

    /**
     * Handle time step completion
     */
    onTimeCompleted(scheduledDateTime: Date): void {
        this.tripData.update((current) => ({
            ...current,
            scheduledDateTime,
        }));
        this.proceedToNextStep();
    }

    /**
     * Handle near pickup location step completion
     */
    onNearPickupLocationCompleted(data: {
        location: LatLng;
        address?: string;
    }): void {
        this.tripData.update((current) => ({
            ...current,
            nearPickupLocation: data.location,
            nearPickupAddress: data.address,
        }));
        this.proceedToNextStep();
    }

    /**
     * Proceed to next step
     */
    proceedToNextStep(): void {
        const current = this.currentStep();
        switch (current) {
            case ScheduledTripStep.ORDER_LOCATION:
                if (this.canProceedToTime()) {
                    this.currentStep.set(ScheduledTripStep.TIME);
                }
                break;
            case ScheduledTripStep.TIME:
                if (this.canProceedToNearPickup()) {
                    this.currentStep.set(
                        ScheduledTripStep.NEAR_PICKUP_LOCATION,
                    );
                }
                break;
            case ScheduledTripStep.NEAR_PICKUP_LOCATION:
                if (this.canConfirmTrip()) {
                    this.currentStep.set(
                        ScheduledTripStep.CONFIRM_SCHEDULED_TRIP,
                    );
                }
                break;
        }
    }

    /**
     * Go back to previous step
     */
    goBack(): void {
        const current = this.currentStep();
        switch (current) {
            case ScheduledTripStep.TIME:
                this.currentStep.set(ScheduledTripStep.ORDER_LOCATION);
                break;
            case ScheduledTripStep.NEAR_PICKUP_LOCATION:
                this.currentStep.set(ScheduledTripStep.TIME);
                break;
            case ScheduledTripStep.CONFIRM_SCHEDULED_TRIP:
                this.currentStep.set(ScheduledTripStep.NEAR_PICKUP_LOCATION);
                break;
        }
    }

    /**
     * Confirm and create scheduled trip
     */
    confirmScheduledTrip(): void {
        const data = this.tripData();
        console.log('Creating scheduled trip with data:', data);

        // TODO: Implement scheduled trip creation logic
        this.services.MessageService.add({
            severity: 'success',
            summary: 'Scheduled Trip Created',
            detail: 'Your trip has been scheduled successfully!',
        });

        // Navigate back or to appropriate page
        this.services.Router.navigate(['/main']);
    }

    /**
     * Start over - reset everything
     */
    startOver(): void {
        this.tripData.set({});
        this.currentStep.set(ScheduledTripStep.ORDER_LOCATION);
    }
}
