<div class="flex flex-1 flex-col bg-background-color-100">
    <!-- Step Header -->
    <div
        class="flex items-center gap-4 border-b border-background-color-300 p-6 ps-8"
    >
        <div
            class="flex h-12 w-12 items-center justify-center rounded-full bg-main-color-600 text-white"
        >
            <i class="pi pi-map-marker text-lg"></i>
        </div>
        <div>
            <h3 class="text-xl font-semibold text-text-color-100">
                Set Your Trip Route
            </h3>
            <p class="text-sm text-text-color-300">
                Choose your pickup and destination locations
            </p>
        </div>
    </div>

    <!-- Current Step Indicator -->
    @if (currentLocationStep() === "pickup") {
        <!-- Pickup Location Selection -->
        <div
            class="flex items-center gap-4 border-b border-background-color-300 p-6"
        >
            <div
                class="flex h-12 w-12 items-center justify-center rounded-full bg-green-600 text-white"
            >
                <i class="pi pi-map-marker text-lg"></i>
            </div>
            <div>
                <h3 class="text-xl font-semibold text-text-color-100">
                    Pick your pickup location
                </h3>
            </div>
        </div>

        <div class="flex h-auto min-h-0 flex-1 flex-col">
            <app-location-picker
                title="Pickup"
                placeholder="Enter pickup location"
                (locationSelected)="onPickupSelected($event)"
            ></app-location-picker>
        </div>
    }

    @if (currentLocationStep() === "dropoff") {
        <!-- Show selected pickup -->
        <div
            class="flex items-center gap-4 border-b border-background-color-300 bg-background-color-100 p-4"
        >
            <div
                class="flex h-10 w-10 items-center justify-center rounded-full bg-green-600 text-white"
            >
                <i class="pi pi-check text-sm"></i>
            </div>
            <div class="min-w-0 flex-1">
                <h4 class="mb-1 text-sm font-medium text-text-color-100">
                    Pickup
                </h4>
                <p class="truncate text-sm text-text-color-300">
                    {{ pickupAddress() || "Selected location" }}
                </p>
            </div>
        </div>

        <!-- Dropoff Location Selection -->
        <div
            class="flex items-center gap-4 border-b border-background-color-300 p-6"
        >
            <div
                class="flex h-12 w-12 items-center justify-center rounded-full bg-red-600 text-white"
            >
                <i class="pi pi-map-marker text-lg"></i>
            </div>
            <div>
                <h3 class="text-xl font-semibold text-text-color-100">
                    Where to?
                </h3>
            </div>
        </div>

        <div class="flex min-h-0 flex-1 flex-col">
            <app-location-picker
                title="Destination"
                placeholder="Enter destination"
                (locationSelected)="onDropoffSelected($event)"
            ></app-location-picker>
        </div>
    }

    @if (currentLocationStep() === "confirm") {
        <!-- Enhanced Route Confirmation with Map -->
        <div class="flex h-full flex-col bg-background-color-100">
            <!-- Header -->
            <div class="flex-shrink-0 p-6 pb-4">
                <div class="text-center">
                    <i
                        class="pi pi-map-marker mb-4 text-4xl text-main-color-600"
                    ></i>
                    <h2 class="mb-2 text-xl font-semibold text-text-color-100">
                        Confirm Your Route
                    </h2>
                    <p class="text-sm text-text-color-300">
                        Review your route and add waypoints if needed
                    </p>
                </div>
            </div>

            <!-- Route Summary -->
            @if (routeInfo() && !isCalculatingRoute()) {
                <div class="flex-shrink-0 px-6 pb-4">
                    <div class="rounded-lg bg-background-color-100 p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                @if (routeInfo()?.totalDistance) {
                                    <div class="text-center">
                                        <div
                                            class="text-lg font-semibold text-text-color-100"
                                        >
                                            {{
                                                routeInfo()!.totalDistance!.toFixed(
                                                    1
                                                )
                                            }}
                                            km
                                        </div>
                                        <div
                                            class="text-xs text-text-color-300"
                                        >
                                            Distance
                                        </div>
                                    </div>
                                }
                                @if (routeInfo()?.totalDuration) {
                                    <div class="text-center">
                                        <div
                                            class="text-lg font-semibold text-text-color-100"
                                        >
                                            {{ routeInfo()!.totalDuration }}
                                            min
                                        </div>
                                        <div
                                            class="text-xs text-text-color-300"
                                        >
                                            Duration
                                        </div>
                                    </div>
                                }
                                <div class="text-center">
                                    <div
                                        class="text-lg font-semibold text-text-color-100"
                                    >
                                        {{ waypointCount() }}
                                    </div>
                                    <div class="text-xs text-text-color-300">
                                        Waypoints
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }

            <!-- Map Container -->
            <div class="flex min-h-0 flex-1 flex-col px-6">
                <div class="relative flex-1 overflow-hidden rounded-lg">
                    @if (isCalculatingRoute()) {
                        <div
                            class="absolute inset-0 z-10 flex items-center justify-center bg-background-color-100 bg-opacity-75"
                        >
                            <div class="text-center">
                                <p-progressSpinner
                                    styleClass="w-8 h-8"
                                    strokeWidth="4"
                                    fill="transparent"
                                    animationDuration="1s"
                                >
                                </p-progressSpinner>
                                <p class="mt-2 text-sm text-text-color-300">
                                    Calculating route...
                                </p>
                            </div>
                        </div>
                    }
                    <div class="h-[80dvh]">
                        <app-map
                            [nodes]="mapLayers()"
                            (clickMap)="onMapClick($event)"
                            class="block h-full w-full"
                        ></app-map>
                    </div>
                </div>
            </div>

            <!-- Waypoints List -->
            @if (routeInfo() && routeInfo()!.waypoints.length > 2) {
                <div class="flex-shrink-0 px-6 pb-4">
                    <div class="rounded-lg bg-background-color-100 p-4">
                        <h3
                            class="mb-3 text-sm font-semibold text-text-color-100"
                        >
                            Waypoints
                        </h3>
                        <div class="space-y-2">
                            @for (
                                waypoint of IntermediateWaypoints();
                                track waypoint.id
                            ) {
                                <div
                                    class="flex items-center justify-between rounded-lg bg-background-color-100 p-3"
                                >
                                    <div class="flex items-center space-x-3">
                                        <div
                                            class="flex h-6 w-6 items-center justify-center rounded-full bg-blue-500 text-xs font-bold text-white"
                                        >
                                            {{ $index + 1 }}
                                        </div>
                                        <div class="min-w-0 flex-1">
                                            <p
                                                class="text-sm text-text-color-100"
                                            >
                                                {{
                                                    waypoint.address ||
                                                        "Waypoint " +
                                                            ($index + 1)
                                                }}
                                            </p>
                                            <p
                                                class="text-xs text-text-color-300"
                                            >
                                                {{
                                                    waypoint.location.lat.toFixed(
                                                        6
                                                    )
                                                }},
                                                {{
                                                    waypoint.location.lng.toFixed(
                                                        6
                                                    )
                                                }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }

            <!-- Instructions -->
            <div class="flex-shrink-0 p-6 pt-4">
                <div class="rounded-lg bg-blue-50 p-3 text-sm text-blue-800">
                    <i class="pi pi-info-circle mr-2"></i>
                    Click on the map to add waypoints to your route
                </div>
            </div>

            <!-- Route Summary -->
            <div class="mb-8 space-y-4">
                <div
                    class="flex items-start gap-4 rounded-lg border border-background-color-300 bg-background-color-100 p-4"
                >
                    <div
                        class="flex h-10 w-10 items-center justify-center rounded-full bg-green-600 text-white"
                    >
                        <i class="pi pi-circle text-sm"></i>
                    </div>
                    <div class="min-w-0 flex-1">
                        <h4
                            class="mb-1 text-sm font-semibold text-text-color-100"
                        >
                            Pickup Location
                        </h4>
                        <p class="text-sm text-text-color-300">
                            {{ pickupAddress() || "Selected location" }}
                        </p>
                    </div>
                </div>

                <div
                    class="flex items-start gap-4 rounded-lg border border-background-color-300 bg-background-color-100 p-4"
                >
                    <div
                        class="flex h-10 w-10 items-center justify-center rounded-full bg-red-600 text-white"
                    >
                        <i class="pi pi-circle text-sm"></i>
                    </div>
                    <div class="min-w-0 flex-1">
                        <h4
                            class="mb-1 text-sm font-semibold text-text-color-100"
                        >
                            Destination
                        </h4>
                        <p class="text-sm text-text-color-300">
                            {{ dropoffAddress() || "Selected location" }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Confirm Button -->
            <button
                class="flex min-h-[56px] w-full cursor-pointer items-center justify-center rounded-lg border-none bg-main-color-600 text-base font-semibold text-white shadow-shadow-200 transition-all duration-200 hover:-translate-y-0.5 hover:bg-main-color-700 hover:shadow-shadow-400 focus:outline-none focus:ring-2 focus:ring-main-color-600 focus:ring-offset-2"
                (click)="confirmLocations()"
            >
                <i class="pi pi-arrow-right mr-2 text-sm"></i>
                Continue to Time Selection
            </button>
        </div>
    }
</div>
